import { logging } from "@skywind-group/sw-utils";
import { createHttpClient, HttpClient } from "../utils/httpClient";
import config from "../config";
import {
    SlackMessage
} from "./domainWatcher/slackMessageFormatter";

const log = logging.logger("slack-notification");

export class SlackNotificationService {
    private readonly httpClient: HttpClient;
    private readonly webhookUrl: string;
    private readonly enabled: boolean;

    constructor(webhookUrl: string, enabled: boolean) {
        this.enabled = enabled;
        this.webhookUrl = webhookUrl;

        if (this.enabled && !this.webhookUrl) {
            log.warn("Slack notifications are enabled but webhook URL is not configured");
            this.enabled = false;
        }

        this.httpClient = createHttpClient({
            timeout: config.slackNotifications.timeout,
            retryConfig: config.slackNotifications.retryConfig,
            headers: {
                "Content-Type": "application/json"
            }
        }, log);
    }

    public async sendFormattedMessage(message: SlackMessage): Promise<void> {
        if (!this.enabled) {
            log.debug("Slack notifications disabled, skipping formatted message");
            return;
        }

        await this.sendMessage(message);
    }

    private async sendMessage(message: SlackMessage): Promise<void> {
        try {
            log.info({ message }, "Sending Slack notification");
            
            await this.httpClient.post(this.webhookUrl, message);
            
            log.info("Slack notification sent successfully");
        } catch (error) {
            log.error({ error: error.message, message }, "Failed to send Slack notification");
            throw error;
        }
    }
}

let slackNotificationService: SlackNotificationService;

export function getSlackNotificationService(): SlackNotificationService {
    if (!slackNotificationService) {
        slackNotificationService = new SlackNotificationService(
            config.slackNotifications.blockedDomainsWebhookUrl,
            config.slackNotifications.enabled
        );
    }
    return slackNotificationService;
}
