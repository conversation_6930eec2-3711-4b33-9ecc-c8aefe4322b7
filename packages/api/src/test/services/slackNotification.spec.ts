import { SinonStub, stub, restore } from "sinon";
import { expect, should } from "chai";
import { SlackNotificationService } from "../../skywind/services/slackNotification";
import { SlackMessage } from "../../skywind/services/domainWatcher/slackMessageFormatter";
import * as httpClientModule from "../../skywind/utils/httpClient";
import config from "../../skywind/config";

should();

class MockHttpClient {
    private sentMessages: any[] = [];

    async post<T>(url: string, data: any): Promise<T> {
        this.sentMessages.push({ url, data });
        return {} as T;
    }

    // Helper methods for testing
    getSentMessages() {
        return this.sentMessages;
    }

    getLastMessage() {
        return this.sentMessages[this.sentMessages.length - 1];
    }

    clear() {
        this.sentMessages = [];
    }
}

describe("SlackNotificationService", () => {
    let createHttpClientStub: SinonStub;
    let httpClient: MockHttpClient;
    let slackService: SlackNotificationService;
    let originalConfig: any;

    before(() => {
        originalConfig = {
            enabled: config.slackNotifications.enabled,
            webhookUrl: config.slackNotifications.blockedDomainsWebhookUrl
        };
        
        config.slackNotifications.enabled = true;
        config.slackNotifications.blockedDomainsWebhookUrl = "https://hooks.slack.com/test";
    });

    beforeEach(() => {
        createHttpClientStub = stub(httpClientModule, "createHttpClient");
        httpClient = new MockHttpClient();
        createHttpClientStub.returns(httpClient);
        slackService = new SlackNotificationService(
            config.slackNotifications.blockedDomainsWebhookUrl,
            config.slackNotifications.enabled
        );
    });

    afterEach(() => {
        restore();
        httpClient.clear();
    });

    after(() => {
        config.slackNotifications.enabled = originalConfig.enabled;
        config.slackNotifications.blockedDomainsWebhookUrl = originalConfig.webhookUrl;
    });

    describe("sendFormattedMessage", () => {
        it("should send formatted message with all fields", async () => {
            const message: SlackMessage = {
                text: "🚫 *Domain Blocked Alert*\n*Domain:* example.com\n*Blocked At:* 2023-01-01T12:00:00.000Z\n*Pool ID:* 456\n*Reason:* Domain blocked by monitoring",
                channel: "#blocked_domains",
                username: "Domain Monitor",
                icon_emoji: ":warning:"
            };

            await slackService.sendFormattedMessage(message);

            const sentMessage = httpClient.getLastMessage();
            expect(sentMessage).to.not.be.undefined;
            expect(sentMessage.data.text).to.include("🚫 *Domain Blocked Alert*");
            expect(sentMessage.data.text).to.include("*Domain:* example.com");
            expect(sentMessage.data.text).to.include("*Blocked At:* 2023-01-01T12:00:00.000Z");
            expect(sentMessage.data.text).to.include("*Pool ID:* 456");
            expect(sentMessage.data.text).to.include("*Reason:* Domain blocked by monitoring");
            expect(sentMessage.data.channel).to.equal("#blocked_domains");
            expect(sentMessage.data.username).to.equal("Domain Monitor");
            expect(sentMessage.data.icon_emoji).to.equal(":warning:");
        });

        it("should send formatted message with minimal fields", async () => {
            const message: SlackMessage = {
                text: "⚠️ *Empty Domain Pool Alert*\n*Pool:* Test Pool (ID: 123)\n*Pool Type:* static\n*Status:* No active, unblocked domains available",
                channel: "#blocked_domains",
                username: "Domain Monitor",
                icon_emoji: ":exclamation:"
            };

            await slackService.sendFormattedMessage(message);

            const sentMessage = httpClient.getLastMessage();
            expect(sentMessage).to.not.be.undefined;
            expect(sentMessage.data.text).to.include("⚠️ *Empty Domain Pool Alert*");
            expect(sentMessage.data.text).to.include("*Pool:* Test Pool (ID: 123)");
            expect(sentMessage.data.text).to.include("*Pool Type:* static");
            expect(sentMessage.data.channel).to.equal("#blocked_domains");
            expect(sentMessage.data.username).to.equal("Domain Monitor");
            expect(sentMessage.data.icon_emoji).to.equal(":exclamation:");
        });
    });



    describe("when Slack notifications are disabled", () => {
        beforeEach(() => {
            slackService = new SlackNotificationService(
                config.slackNotifications.blockedDomainsWebhookUrl,
                false
            );
        });

        it("should not send formatted message when disabled", async () => {
            const message: SlackMessage = {
                text: "🚫 *Domain Blocked Alert*\n*Domain:* example.com",
                channel: "#blocked_domains",
                username: "Domain Monitor",
                icon_emoji: ":warning:"
            };

            await slackService.sendFormattedMessage(message);

            expect(httpClient.getSentMessages()).to.have.length(0);
        });
    });

    describe("when webhook URL is not configured", () => {
        beforeEach(() => {
            slackService = new SlackNotificationService("", true);
        });

        it("should not send formatted message when webhook URL is empty", async () => {
            const message: SlackMessage = {
                text: "🚫 *Domain Blocked Alert*\n*Domain:* example.com",
                channel: "#blocked_domains",
                username: "Domain Monitor",
                icon_emoji: ":warning:"
            };

            await slackService.sendFormattedMessage(message);

            expect(httpClient.getSentMessages()).to.have.length(0);
        });
    });
});
